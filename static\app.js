/**
 * SQL Evaluator Frontend JavaScript
 * Handles user interactions and API communication
 */

class SQLEvaluator {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.loadSystemInfo();
        this.loadSamplePrompts();
    }

    initializeElements() {
        this.userPrompt = document.getElementById('userPrompt');
        this.evaluateBtn = document.getElementById('evaluateBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.resultsSection = document.getElementById('resultsSection');
        this.systemInfo = document.getElementById('systemInfo');
        this.samplePrompts = document.getElementById('samplePrompts');
    }

    bindEvents() {
        this.evaluateBtn.addEventListener('click', () => this.evaluateSQL());
        this.clearBtn.addEventListener('click', () => this.clearResults());
        
        // Allow Enter key to submit (with Ctrl/Cmd for new line)
        this.userPrompt.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.ctrlKey && !e.metaKey) {
                e.preventDefault();
                this.evaluateSQL();
            }
        });
    }

    async loadSystemInfo() {
        try {
            const response = await fetch('/api/system-info');
            const data = await response.json();
            
            if (data.success) {
                this.systemInfo.innerHTML = `
                    <div class="row">
                        <div class="col-md-3">
                            <i class="fas fa-robot text-success"></i>
                            <strong>Performer:</strong> ${data.performer_model}
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-gavel text-danger"></i>
                            <strong>Judge:</strong> ${data.judge_model}
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-database text-info"></i>
                            <strong>Database:</strong> ${data.database_connected ? 'Connected' : 'Disconnected'}
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-table text-warning"></i>
                            <strong>Tables:</strong> ${data.schema_tables_count}
                        </div>
                    </div>
                `;
            } else {
                this.systemInfo.innerHTML = `
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        System Error: ${data.error}
                    </div>
                `;
            }
        } catch (error) {
            this.systemInfo.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Failed to load system information: ${error.message}
                </div>
            `;
        }
    }

    async loadSamplePrompts() {
        try {
            const response = await fetch('/api/sample-prompts');
            const data = await response.json();
            
            if (data.success) {
                this.samplePrompts.innerHTML = data.samples.map(prompt => 
                    `<span class="sample-prompt" onclick="sqlEvaluator.setPrompt('${prompt.replace(/'/g, "\\'")}')">${prompt}</span>`
                ).join('');
            }
        } catch (error) {
            console.error('Failed to load sample prompts:', error);
        }
    }

    setPrompt(prompt) {
        this.userPrompt.value = prompt;
        this.userPrompt.focus();
    }

    async evaluateSQL() {
        const prompt = this.userPrompt.value.trim();
        
        if (!prompt) {
            this.showAlert('Please enter a prompt', 'warning');
            return;
        }

        this.setLoading(true);
        this.hideResults();

        try {
            const response = await fetch('/api/evaluate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt: prompt })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
            } else {
                this.showAlert(`Evaluation failed: ${data.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`Network error: ${error.message}`, 'danger');
        } finally {
            this.setLoading(false);
        }
    }

    displayResults(data) {
        // Update performer results
        document.getElementById('performerModel').textContent = data.performer.model;
        document.getElementById('performerTime').textContent = `${data.performer.execution_time}s`;
        document.getElementById('generatedSQL').textContent = data.performer.generated_sql;

        // Update judge results
        document.getElementById('judgeModel').textContent = data.judge.model;
        document.getElementById('judgeTime').textContent = `${data.judge.execution_time}s`;
        document.getElementById('totalTime').textContent = data.total_execution_time;

        const evaluation = data.judge.evaluation;

        // Final verdict
        const verdictElement = document.getElementById('finalVerdict');
        verdictElement.textContent = evaluation.final_verdict;
        verdictElement.className = `score-badge ${evaluation.final_verdict === 'PASS' ? 'verdict-pass' : 'verdict-fail'}`;

        // Correctness
        this.updateScore('correctness', evaluation.correctness_score, evaluation.correctness_reasoning);

        // Optimization
        this.updateScore('optimization', evaluation.optimization_score, evaluation.optimization_reasoning);

        // Security
        const securityElement = document.getElementById('securityStatus');
        securityElement.textContent = evaluation.is_safe ? 'SAFE' : 'UNSAFE';
        securityElement.className = `score-badge ${evaluation.is_safe ? 'score-excellent' : 'score-poor'}`;
        document.getElementById('securityReasoning').textContent = evaluation.security_reasoning;

        this.showResults();
    }

    updateScore(type, score, reasoning) {
        const scoreElement = document.getElementById(`${type}Score`);
        const reasoningElement = document.getElementById(`${type}Reasoning`);

        scoreElement.textContent = `${score}/5`;
        scoreElement.className = `score-badge ${this.getScoreClass(score)}`;
        reasoningElement.textContent = reasoning;
    }

    getScoreClass(score) {
        if (score >= 4) return 'score-excellent';
        if (score >= 3) return 'score-good';
        if (score >= 2) return 'score-average';
        return 'score-poor';
    }

    setLoading(loading) {
        if (loading) {
            this.loadingIndicator.style.display = 'block';
            this.evaluateBtn.disabled = true;
            this.evaluateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        } else {
            this.loadingIndicator.style.display = 'none';
            this.evaluateBtn.disabled = false;
            this.evaluateBtn.innerHTML = '<i class="fas fa-play"></i> Generate & Evaluate SQL';
        }
    }

    showResults() {
        this.resultsSection.style.display = 'block';
        this.resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    hideResults() {
        this.resultsSection.style.display = 'none';
    }

    clearResults() {
        this.userPrompt.value = '';
        this.hideResults();
        this.userPrompt.focus();
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at the top of the main container
        const mainContainer = document.querySelector('.main-container .p-4');
        mainContainer.insertBefore(alertDiv, mainContainer.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.sqlEvaluator = new SQLEvaluator();
});

// Add some utility functions for better UX
document.addEventListener('DOMContentLoaded', () => {
    // Add smooth scrolling for better UX
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + Enter to evaluate
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (window.sqlEvaluator) {
                window.sqlEvaluator.evaluateSQL();
            }
        }
        
        // Escape to clear
        if (e.key === 'Escape') {
            if (window.sqlEvaluator) {
                window.sqlEvaluator.clearResults();
            }
        }
    });
});
