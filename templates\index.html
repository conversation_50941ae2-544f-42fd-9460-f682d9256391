<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Evaluator - Agent-Based LLM Evaluation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1400px;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .system-info {
            background: rgba(79, 172, 254, 0.1);
            border: 1px solid rgba(79, 172, 254, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .result-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .performer-card {
            border-left: 5px solid #28a745;
        }
        
        .judge-card {
            border-left: 5px solid #dc3545;
        }
        
        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .evaluation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .evaluation-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #007bff;
        }
        
        .score-badge {
            font-size: 1.2em;
            font-weight: bold;
            padding: 5px 12px;
            border-radius: 20px;
        }
        
        .score-excellent { background: #d4edda; color: #155724; }
        .score-good { background: #d1ecf1; color: #0c5460; }
        .score-average { background: #fff3cd; color: #856404; }
        .score-poor { background: #f8d7da; color: #721c24; }
        
        .verdict-pass { 
            background: #d4edda; 
            color: #155724; 
            border: 2px solid #c3e6cb;
        }
        .verdict-fail { 
            background: #f8d7da; 
            color: #721c24; 
            border: 2px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .sample-prompts {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .sample-prompt {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .sample-prompt:hover {
            background: #007bff;
            color: white;
            transform: translateY(-2px);
        }
        
        .execution-time {
            font-size: 0.9em;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-database"></i> SQL Evaluator</h1>
                <p class="mb-0">Agent-Based LLM Evaluation System for SQL Query Generation</p>
            </div>
            
            <div class="p-4">
                <!-- System Information -->
                <div class="system-info" id="systemInfo">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>Loading system information...</span>
                    </div>
                </div>
                
                <!-- Input Section -->
                <div class="input-section">
                    <h4><i class="fas fa-edit"></i> Enter Your SQL Query Request</h4>
                    <p class="text-muted">Describe what you want to query from the database in natural language.</p>
                    
                    <div class="mb-3">
                        <textarea 
                            id="userPrompt" 
                            class="form-control" 
                            rows="3" 
                            placeholder="Example: Show me all users from the backend system"
                            style="border-radius: 10px; border: 2px solid #e9ecef; font-size: 16px;"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <button id="evaluateBtn" class="btn btn-primary btn-lg px-4" style="border-radius: 25px;">
                            <i class="fas fa-play"></i> Generate & Evaluate SQL
                        </button>
                        <button id="clearBtn" class="btn btn-outline-secondary" style="border-radius: 25px;">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                    
                    <!-- Sample Prompts -->
                    <div class="mt-3">
                        <small class="text-muted">Quick examples:</small>
                        <div class="sample-prompts" id="samplePrompts">
                            <!-- Sample prompts will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div class="loading" id="loadingIndicator">
                    <div class="spinner"></div>
                    <h5>Processing your request...</h5>
                    <p class="text-muted">The Performer is generating SQL, then the Judge will evaluate it.</p>
                </div>
                
                <!-- Results Section -->
                <div class="results-section" id="resultsSection" style="display: none;">
                    <!-- Performer Results -->
                    <div class="card result-card performer-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-robot text-success"></i> 
                                Performer Agent - SQL Generation
                                <span class="execution-time float-end" id="performerTime"></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Model:</strong> <span id="performerModel" class="badge bg-success"></span>
                            </div>
                            <div class="mb-3">
                                <strong>Generated SQL Query:</strong>
                                <div class="sql-code" id="generatedSQL"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Judge Results -->
                    <div class="card result-card judge-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-gavel text-danger"></i> 
                                Judge Agent - Evaluation Results
                                <span class="execution-time float-end" id="judgeTime"></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Model:</strong> <span id="judgeModel" class="badge bg-danger"></span>
                            </div>
                            
                            <!-- Final Verdict -->
                            <div class="mb-4 text-center">
                                <div id="finalVerdict" class="score-badge verdict-pass" style="font-size: 1.5em; padding: 15px 30px;">
                                    <!-- Verdict will be inserted here -->
                                </div>
                            </div>
                            
                            <!-- Evaluation Details -->
                            <div class="evaluation-grid">
                                <div class="evaluation-item">
                                    <h6><i class="fas fa-check-circle"></i> Correctness</h6>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Score:</span>
                                        <span id="correctnessScore" class="score-badge"></span>
                                    </div>
                                    <p id="correctnessReasoning" class="mb-0 small text-muted"></p>
                                </div>
                                
                                <div class="evaluation-item">
                                    <h6><i class="fas fa-tachometer-alt"></i> Optimization</h6>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Score:</span>
                                        <span id="optimizationScore" class="score-badge"></span>
                                    </div>
                                    <p id="optimizationReasoning" class="mb-0 small text-muted"></p>
                                </div>
                                
                                <div class="evaluation-item">
                                    <h6><i class="fas fa-shield-alt"></i> Security</h6>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Status:</span>
                                        <span id="securityStatus" class="score-badge"></span>
                                    </div>
                                    <p id="securityReasoning" class="mb-0 small text-muted"></p>
                                </div>
                            </div>
                            
                            <div class="mt-3 text-center">
                                <small class="text-muted">
                                    Total execution time: <span id="totalTime" class="fw-bold"></span> seconds
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-sql.min.js"></script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
