[tool.poetry]
name = "SQL_evaluater"
version = "0.1.0"
description = "An LLM agent to evaluate SQL generation tasks."
authors = ["<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.9"
google-generativeai = "^0.5.4"
pyyaml = "^6.0"
pandas = "^2.0"
python-dotenv = "^1.0.0"
psycopg2-binary = "^2.9.9" # For PostgreSQL connection
poetry-plugin-shell = "^1.0.1"
pathlib = "^1.0.1"
langchain = "^0.2.0"
langchain-google-genai = "^1.0.3"
langchain-core = "^0.2.1"
sqlalchemy = "^2.0.41"
flask = "^3.0.0"
flask-cors = "^4.0.0"
# Optional LLM providers - install as needed
langchain-openai = { version = "^0.1.0", optional = true }
langchain-anthropic = { version = "^0.1.0", optional = true }

[tool.poetry.extras]
openai = ["langchain-openai"]
anthropic = ["langchain-anthropic"]
all-llms = ["langchain-openai", "langchain-anthropic"]

[tool.poetry.group.dev.dependencies]
# Dev-only dependencies, not installed in production
jupyterlab = "^4.0"
ipykernel = "^6.25.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

