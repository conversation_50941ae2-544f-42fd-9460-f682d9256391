# Multi-LLM Support Guide

This project now supports multiple LLM providers through a flexible factory system. You can easily switch between Google Gemini, OpenAI, and Anthropic models without changing your code.

## Supported LLM Providers

### 🤖 Google Gemini (Default)

- **Models**: `gemini-2.0-flash`, `gemini-1.5-pro`, `gemini-1.5-flash`, `gemini-pro`, `gemini-pro-vision`
- **Package**: `langchain-google-genai` (already installed)
- **API Key**: Set `GOOGLE_API_KEY` environment variable

### 🧠 OpenAI

- **Models**: `gpt-4o`, `gpt-4o-mini`, `gpt-4-turbo`, `gpt-4`, `gpt-3.5-turbo`
- **Package**: `langchain-openai` (optional)
- **API Key**: Set `OPENAI_API_KEY` environment variable

### 🎭 Anthropic Claude

- **Models**: `claude-3-5-sonnet-20241022`, `claude-3-5-haiku-20241022`, `claude-3-opus-20240229`, `claude-3-sonnet-20240229`, `claude-3-haiku-20240307`
- **Package**: `langchain-anthropic` (optional)
- **API Key**: Set `ANTHROPIC_API_KEY` environment variable

## Installation

### Basic Installation (Gemini only)

```bash
poetry install
```

### With OpenAI Support

```bash
poetry install --extras openai
```

### With Anthropic Support

```bash
poetry install --extras anthropic
```

### With All LLM Providers

```bash
poetry install --extras all-llms
```

## Configuration

### Method 1: Update config.yaml (Recommended)

```yaml
# Use Google Gemini (default)
performer_llm:
  model_name: "gemini-2.0-flash"
  temperature: 0.1

judge_llm:
  model_name: "gemini-1.5-pro"
  temperature: 0.0

# Use OpenAI
performer_llm:
  model_name: "gpt-4o-mini"
  temperature: 0.1
  max_tokens: 1000

judge_llm:
  model_name: "gpt-4o"
  temperature: 0.0
  max_tokens: 2000

# Use Anthropic Claude
performer_llm:
  model_name: "claude-3-5-haiku-20241022"
  temperature: 0.1
  max_tokens: 1000

judge_llm:
  model_name: "claude-3-5-sonnet-20241022"
  temperature: 0.0
  max_tokens: 2000

# Mix different providers!
performer_llm:
  model_name: "gemini-2.0-flash"  # Google
  temperature: 0.1

judge_llm:
  model_name: "gpt-4o-mini"       # OpenAI
  temperature: 0.0
  api_key: "your-openai-key"      # Optional if set in environment
```

### Method 2: Environment Variables

Set your API keys:

```bash
# For Google Gemini
export GOOGLE_API_KEY="your-google-api-key"

# For OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# For Anthropic
export ANTHROPIC_API_KEY="your-anthropic-api-key"
```

## Key Improvements

### ✅ Deprecation Warning Fixed

- **Problem**: `convert_system_message_to_human=True` is deprecated in Google Gemini
- **Solution**: The new system automatically handles system messages differently for each provider:
  - **Gemini**: Combines system and human messages into a single human message
  - **OpenAI/Anthropic**: Uses separate system and human messages as intended

### ✅ Flexible Provider Support

- **Before**: Hard-coded to use only Google Gemini
- **After**: Automatic provider detection based on model name
- **Benefit**: Easy to switch between providers without code changes

## Usage Examples

### Basic Usage (No Code Changes Required)

```python
# Your existing code still works!
from sql_judge_agent.judge import SQLJudge

judge = GeminiSQLJudge(config['judge_llm'])
```

### New Flexible Usage

```python
from sql_judge_agent.judge import SQLJudge

# Works with any supported model
judge = SQLJudge(config['judge_llm'])
```

### Direct LLM Creation

```python
from llm_factory import create_llm

# Automatically detects provider from model name
llm = create_llm({
    'model_name': 'gpt-4o-mini',
    'temperature': 0.1
})
```

### Check Supported Models

```python
from llm_factory import get_supported_models

models = get_supported_models()
print(models)
# Output:
# {
#   'google': ['gemini-2.0-flash', 'gemini-1.5-pro', ...],
#   'openai': ['gpt-4o', 'gpt-4o-mini', ...],
#   'anthropic': ['claude-3-5-sonnet-20241022', ...]
# }
```

## Error Handling

The system provides clear error messages:

```python
# Missing package
ValueError: langchain-openai is required for OpenAI models

# Unsupported model
ValueError: Unsupported model: gpt-5. Supported models: [...]

# Missing API key
AuthenticationError: OpenAI API key not found
```

## Migration Guide

### From Old System

1. **No immediate changes required** - your existing code continues to work
2. **Optional**: Replace `GeminiSQLJudge` with `SQLJudge` for consistency
3. **Optional**: Install additional LLM providers as needed

### Best Practices

1. **Use environment variables** for API keys (more secure)
2. **Test with cheaper models first** (e.g., `gpt-4o-mini`, `claude-3-haiku`)
3. **Mix providers** based on your needs (e.g., fast model for generation, powerful model for evaluation)

## Troubleshooting

### Common Issues

1. **Import Error**: Install the required package (`poetry install --extras openai`)
2. **Authentication Error**: Set the appropriate API key environment variable
3. **Model Not Found**: Check the supported models list above

### Getting Help

- Check the error message - they're designed to be helpful
- Verify your API keys are set correctly
- Ensure you've installed the required packages
