# run_evaluation.py
from sql_judge_agent.judge import S<PERSON>J<PERSON>
from sql_judge_agent.performer import SQL<PERSON>erformer
from sql_judge_agent.utils import get_db_schema
from config_utils import load_db_config, load_full_config
import sys
import pprint
from pathlib import Path

# Add root directory to Python path for clean imports
sys.path.insert(0, str(Path(__file__).parent))


# --- Main Execution ---
if __name__ == "__main__":
    # 1. Load configuration from config.yaml
    config = load_full_config()
    print("✅ Configuration loaded from config.yaml")

    # 2. Define the user's natural language question
    # --- Try changing this prompt to test different scenarios! ---
    user_prompt = "Return the first 5 rows from the bkr users table"

    print("======================================================================")
    print(f"▶️  Starting Evaluation for Prompt: '{user_prompt}'")
    print("======================================================================")

    # 3. Get Database Schema
    print("\nSTEP 1: Fetching database schema...")
    # Load database configuration
    db_config = load_db_config()
    db_schema = get_db_schema(db_config)

    if not db_schema:
        print("❌ ERROR: database schema fetch failure.")
        exit()

    # 4. Generate the SQL Query (The "Performer")
    print("\nSTEP 2: Generating SQL query with the Performer...")
    performer_config = config['performer_llm']
    performer = SQLPerformer(performer_config)

    generated_sql, performer_time = performer.generate_sql(
        db_schema, user_prompt)

    print("✅ Performer generated the following SQL:")
    print("----------------------------------------------------------------------")
    print(generated_sql)
    print("----------------------------------------------------------------------")
    print(f"⏱️ Performer execution time: {performer_time:.2f} seconds")

    # 5. Evaluate the Generated SQL (The "Judge" LLM)
    print("\nSTEP 3: Evaluating generated SQL with the Judge LLM...")
    judge = SQLJudge(config['judge_llm'])

    try:
        evaluation_result = judge.evaluate(
            schema=db_schema,
            user_prompt=user_prompt,
            generated_sql=generated_sql
        )
        print("✅ Judge LLM provided the following evaluation:")
        print("----------------------------------------------------------------------")
        # Pretty print the Pydantic object
        pprint.pprint(evaluation_result.model_dump())
        print("----------------------------------------------------------------------")

    except Exception as e:
        print(f"❌ An error occurred during evaluation: {e}")

    print("\n✅ Evaluation complete.")
