#!/usr/bin/env python3
"""
SQL Evaluator Web Application Launcher
Simple script to start the Flask web application with proper error handling.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        'flask',
        'flask_cors',
        'yaml',
        'pandas',
        'psycopg2',
        'langchain',
        'langchain_core',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   poetry install")
        print("   # or")
        print("   pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_configuration():
    """Check if configuration files exist."""
    config_file = current_dir / 'config.yaml'
    
    if not config_file.exists():
        print("❌ Configuration file 'config.yaml' not found!")
        print("💡 Please create config.yaml with your LLM and database settings.")
        print("   See the README.md for configuration examples.")
        return False
    
    return True

def main():
    """Main function to start the web application."""
    print("🚀 SQL Evaluator Web Application")
    print("=" * 50)
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ All dependencies found")
    
    # Check configuration
    print("🔍 Checking configuration...")
    if not check_configuration():
        sys.exit(1)
    print("✅ Configuration file found")
    
    # Import and run the Flask app
    try:
        print("🌐 Starting web server...")
        from app import app, initialize_system
        
        # Initialize the system
        if initialize_system():
            print("✅ System initialized successfully!")
            print("\n" + "=" * 50)
            print("🎉 SQL Evaluator is ready!")
            print("🌐 Open your browser and go to: http://localhost:5000")
            print("⌨️  Press Ctrl+C to stop the server")
            print("=" * 50)
            
            # Start the Flask development server
            app.run(
                debug=True,
                host='0.0.0.0',
                port=5000,
                use_reloader=False  # Disable reloader to avoid double initialization
            )
        else:
            print("❌ Failed to initialize system. Please check your configuration.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 Shutting down SQL Evaluator...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
