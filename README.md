# SQL Evaluator: Agent-Based LLM Evaluation System

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Poetry](https://img.shields.io/badge/Poetry-Dependency%20Management-blue.svg)](https://python-poetry.org/)
[![<PERSON><PERSON>hain](https://img.shields.io/badge/LangChain-Framework-green.svg)](https://langchain.com)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Database-blue.svg)](https://postgresql.org)

An advanced **Agent-Based Evaluation System** for assessing SQL query generation capabilities of Large Language Models (LLMs). This project implements a dual-agent architecture where one LLM generates SQL queries (Performer) and another LLM evaluates the quality, correctness, and security of those queries (Judge).

![SQL Evaluator Architecture](images/Sieve.jpg)

## 🎯 Project Purpose

This system addresses the critical need for **automated, scalable, and consistent evaluation** of AI-generated SQL queries. Traditional manual evaluation methods are:

- ❌ **Time-consuming** and don't scale
- ❌ **Inconsistent** due to human subjectivity
- ❌ **Expensive** requiring domain experts
- ❌ **Limited** in coverage and depth

Our solution provides:

- ✅ **Automated evaluation** at scale
- ✅ **Multi-dimensional assessment** (correctness, optimization, security)
- ✅ **Structured, machine-readable results**
- ✅ **Flexible LLM provider support** (Google Gemini, OpenAI, Anthropic)
- ✅ **Real-time database schema integration**

## 🏗️ Architecture Overview

### Core Components

```mermaid
graph TB
    A[User Natural Language Query] --> B[Database Schema Fetcher]
    B --> C[Performer LLM Agent]
    C --> D[Generated SQL Query]
    D --> E[Judge LLM Agent]
    E --> F[Structured Evaluation Results]

    G[Configuration System] --> C
    G --> E
    H[LLM Factory] --> C
    H --> E
    I[PostgreSQL Database] --> B

    subgraph "Evaluation Criteria"
        J[Correctness Score 1-5]
        K[Optimization Score 1-5]
        L[Security Assessment]
        M[Final Verdict PASS/FAIL]
    end

    E --> J
    E --> K
    E --> L
    E --> M
```

### 1. **Performer Agent** 🎭

- **Role**: SQL Query Generator
- **Input**: Natural language question + Database schema
- **Output**: Syntactically correct SQL query
- **Models**: Supports any LLM (Gemini, GPT, Claude)

### 2. **Judge Agent** ⚖️

- **Role**: SQL Query Evaluator
- **Input**: Original question + Generated SQL + Database schema
- **Output**: Structured evaluation with scores and reasoning
- **Evaluation Dimensions**:
  - **Correctness** (1-5): Logic, joins, filtering accuracy
  - **Optimization** (1-5): Performance, efficiency, index usage
  - **Security** (Safe/Unsafe): Injection risks, destructive operations
  - **Final Verdict**: PASS/FAIL decision

### 3. **LLM Factory** 🏭

- **Flexible Provider Support**: Auto-detects provider from model name
- **Supported Providers**:
  - **Google Gemini**: `gemini-2.0-flash`, `gemini-1.5-pro`, `gemini-1.5-flash`
  - **OpenAI**: `gpt-4o`, `gpt-4o-mini`, `gpt-4-turbo`, `gpt-3.5-turbo`
  - **Anthropic**: `claude-3-5-sonnet-20241022`, `claude-3-5-haiku-20241022`

### 4. **Configuration System** ⚙️

- **Dual Loading**: YAML config files + Environment variables
- **Database Integration**: PostgreSQL connection management
- **LLM Settings**: Model selection, temperature, token limits

## 🛠️ Technologies Used

### Core Framework

- **Python 3.9+**: Main programming language
- **LangChain**: LLM orchestration and chaining framework
- **Pydantic**: Data validation and structured output parsing
- **Poetry**: Dependency management and packaging

### LLM Integration

- **langchain-google-genai**: Google Gemini models
- **langchain-openai**: OpenAI GPT models (optional)
- **langchain-anthropic**: Anthropic Claude models (optional)

### Database & Data Processing

- **PostgreSQL**: Primary database system
- **psycopg2-binary**: PostgreSQL adapter
- **SQLAlchemy**: Database ORM and connection management
- **pandas**: Data manipulation and CSV processing

### Configuration & Utilities

- **PyYAML**: YAML configuration file parsing
- **python-dotenv**: Environment variable management
- **pathlib**: Modern file path handling

### Web Interface

- **Flask**: Lightweight web framework for the user interface
- **Flask-CORS**: Cross-Origin Resource Sharing support
- **Bootstrap 5**: Modern CSS framework for responsive design
- **Font Awesome**: Icon library for enhanced UI

### Development Tools

- **JupyterLab**: Interactive development and debugging
- **ipykernel**: Jupyter notebook kernel
- **Bruno**: API testing and documentation

### Monitoring & Observability

- **Langfuse**: LLM observability and tracing integration
- **Built-in Performance Metrics**: Timing and cost tracking
- **Structured Logging**: Comprehensive evaluation audit trails

## 📁 Project Structure

```
SQL_evaluater/
├── 📁 sql_judge_agent/          # Core agent implementation
│   ├── judge.py                 # Judge LLM agent & evaluation logic
│   ├── performer.py             # Performer LLM agent
│   ├── utils.py                 # Database utilities & schema fetching
│   └── 📁 prompts/              # Evaluation prompts & rubrics
│       ├── judge_rubric.md      # Detailed evaluation criteria
│       └── performer_prompt.md  # SQL generation prompts
├── 📁 data/                     # Database setup & sample data
│   ├── setup_database.py        # Automated database setup
│   ├── schema.sql              # Database schema definitions
│   └── 📁 SQL/                 # Sample CSV data files
│       ├── 📁 Backend/         # Backend application tables
│       └── 📁 Flowable/        # Workflow engine tables
├── 📁 images/                   # Documentation assets
├── 📁 notebooks/                # Jupyter analysis notebooks
├── 📁 results/                  # Evaluation results & logs
├── 📁 templates/                # HTML templates for web interface
│   └── index.html              # Main web interface template
├── 📁 static/                   # Static web assets (CSS, JS)
│   └── app.js                  # Frontend JavaScript application
├── llm_factory.py              # Multi-provider LLM factory
├── config_utils.py             # Configuration loading utilities
├── config_loaders.py           # Flexible config loaders
├── run_evaluation.py           # Command-line execution script
├── app.py                      # Flask web application
├── run_web_app.py              # Web application launcher
├── config.yaml                 # Main configuration file
├── pyproject.toml              # Poetry dependencies
└── README.md                   # This file
```

## 🚀 Quick Start Guide

### Prerequisites

- **Python 3.9+** installed
- **PostgreSQL** database server running
- **Poetry** for dependency management
- **API Keys** for your chosen LLM provider(s)

### 1. Clone & Setup

```bash
# Clone the repository
git clone https://gitlab-st.proxym-group.net/bankerise-pfe/bankerise-pfe-data/bankerise-ete-data/agent-based-evaluation.git
cd SQL_evaluater

# Install Poetry (if not already installed)
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Optional: Install additional LLM providers
poetry install --extras "openai anthropic"  # For OpenAI + Anthropic
poetry install --extras "all-llms"          # For all providers
```

### 2. Database Setup

```bash
# Start PostgreSQL service
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS

# Create database
createdb proxym

# Setup tables and import sample data
poetry run python data/setup_database.py
```

### 3. Configuration

#### Option A: Using config.yaml (Recommended)

```yaml
# config.yaml
performer_llm:
  model_name: "gemini-2.0-flash"
  temperature: 0.1

judge_llm:
  model_name: "gemini-2.0-flash"
  temperature: 0.0

database:
  name: "proxym"
  user: "postgres"
  password: "your_password"
  host: "localhost"
  port: "5432"
```

#### Option B: Using Environment Variables

```bash
# Create .env file
echo "GOOGLE_API_KEY=your_gemini_api_key" > .env
echo "OPENAI_API_KEY=your_openai_api_key" >> .env
echo "ANTHROPIC_API_KEY=your_claude_api_key" >> .env

# Database configuration
echo "DB_NAME=proxym" >> .env
echo "DB_USER=postgres" >> .env
echo "DB_PASSWORD=your_password" >> .env
echo "DB_HOST=localhost" >> .env
echo "DB_PORT=5432" >> .env
```

### 4. Run Evaluation

#### Option A: Web Interface (Recommended)

**Quick Start Scripts:**

```bash
# Windows
start_web_app.bat

# Linux/macOS
./start_web_app.sh
```

**Manual Start:**

```bash
# Start the web application
python run_web_app.py

# Or with Poetry
poetry run python run_web_app.py

# Open your browser and go to: http://localhost:5000
```

#### Option B: Command Line

```bash
# Activate Poetry environment
poetry shell

# Run the main evaluation
python run_evaluation.py

# Or run directly with Poetry
poetry run python run_evaluation.py
```

## 🌐 Web Interface Features

The web interface provides an intuitive way to interact with the SQL Evaluator:

### ✨ Key Features

- **🎨 Modern UI**: Clean, responsive design with real-time feedback
- **⚡ Real-time Evaluation**: See Performer and Judge results instantly
- **📊 Visual Scoring**: Color-coded evaluation metrics and scores
- **🔍 Detailed Analysis**: Comprehensive breakdown of correctness, optimization, and security
- **💡 Sample Prompts**: Quick-start examples for testing
- **⌨️ Keyboard Shortcuts**:
  - `Enter` to submit (Ctrl/Cmd+Enter for new line)
  - `Ctrl/Cmd+Enter` to evaluate from anywhere
  - `Escape` to clear results

### 🖥️ Interface Sections

1. **System Information**: Shows active LLM models and database status
2. **Input Section**: Natural language prompt input with sample suggestions
3. **Performer Results**: Generated SQL query with execution time
4. **Judge Results**: Detailed evaluation with scores and reasoning

### 📱 Responsive Design

- Works on desktop, tablet, and mobile devices
- Optimized for both development and presentation use

## 🎮 Usage Examples

### Web Interface Usage

1. Open http://localhost:5000 in your browser
2. Enter a natural language query (e.g., "Show me all users from the backend system")
3. Click "Generate & Evaluate SQL" or press Enter
4. View the Performer's generated SQL and Judge's evaluation

### Basic Evaluation (Programmatic)

```python
from sql_judge_agent.judge import SQLJudge
from sql_judge_agent.utils import get_db_schema
from llm_factory import create_llm
from config_utils import load_full_config

# Load configuration
config = load_full_config()

# Create judge agent
judge = SQLJudge(config['judge_llm'])

# Evaluate a query
result = judge.evaluate(
    schema=db_schema,
    user_prompt="Show me all users from the backend system",
    generated_sql="SELECT * FROM backend.bkr_users LIMIT 10;"
)

print(result.model_dump())
```

### Custom LLM Configuration

```python
# Using different models for performer vs judge
config = {
    'performer_llm': {
        'model_name': 'gpt-4o-mini',
        'temperature': 0.1
    },
    'judge_llm': {
        'model_name': 'claude-3-5-sonnet-20241022',
        'temperature': 0.0
    }
}
```

### Interactive Jupyter Analysis

```bash
# Start Jupyter Lab
poetry run jupyter lab

# Open notebooks/test-performer.ipynb for interactive testing
# Open notebooks/debug-judge-logic.ipynb for evaluation debugging
```

### API Testing with Bruno

The project includes Bruno HTTP request collections for API testing:

```bash
# Install Bruno (if not already installed)
npm install -g @usebruno/cli

# Navigate to the HTTP requests directory
cd "SQL Evaluater HTTP requests"

# Run API tests
bruno run
```

**Available API Endpoints:**

- `Fetch System Info.bru`: Test system information endpoint
- `Fetch Suggested Sample prompts.bru`: Test sample prompts endpoint

## 📊 Sample Output

```json
{
  "correctness_score": 4,
  "correctness_reasoning": "Query correctly selects from bkr_users table with appropriate LIMIT clause. Logic is sound for retrieving user data.",
  "optimization_score": 5,
  "optimization_reasoning": "Efficient query with LIMIT clause to prevent large result sets. No unnecessary joins or subqueries.",
  "is_safe": true,
  "security_reasoning": "Read-only SELECT query with no destructive operations. No SQL injection risks detected.",
  "final_verdict": "PASS"
}
```

## 🔧 Advanced Configuration

### Multi-Provider Setup

```yaml
# config.yaml - Mix and match providers
performer_llm:
  model_name: "gpt-4o-mini" # OpenAI for generation
  temperature: 0.1
  api_key: "your-openai-key" # Optional if in environment

judge_llm:
  model_name: "claude-3-5-sonnet-20241022" # Anthropic for evaluation
  temperature: 0.0
  api_key: "your-anthropic-key" # Optional if in environment
```

### Custom Database Connection

```python
# Custom database configuration
db_config = {
    'host': 'your-db-host.com',
    'port': 5432,
    'database': 'your_database',
    'user': 'your_user',
    'password': 'your_password'
}
```

## 🧪 Testing & Development

### Run Tests

```bash
# Run basic functionality test
poetry run python test.py

# Interactive testing with Jupyter
poetry run jupyter lab notebooks/test-performer.ipynb
```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
poetry run python run_evaluation.py
```

### Custom Evaluation Prompts

Edit `sql_judge_agent/prompts/judge_rubric.md` to customize evaluation criteria and scoring rubrics.

## 📈 Performance & Scalability

### Optimization Tips

- Use **faster models** for development (`gemini-2.0-flash`, `gpt-4o-mini`)
- Use **premium models** for production (`gemini-2.5-pro`, `gpt-o3`, `claude-3-5-sonnet`)
- **Cache database schemas** for repeated evaluations
- **Batch API calls** for large-scale evaluations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Dhia Ben Hamouda**

- Email: <EMAIL>
- Organization: Proxym IT - ISIMM
- LinkedIn: https://www.linkedin.com/in/dhiabenhamouda/

## 🔗 Repository

**GitLab Repository:** https://gitlab-st.proxym-group.net/bankerise-pfe/bankerise-pfe-data/bankerise-ete-data/agent-based-evaluation.git

This repository contains the complete implementation with:

- ✅ Complete source code with comprehensive documentation
- ✅ Sample data and automated database setup
- ✅ Interactive Jupyter notebooks for development
- ✅ Web interface for real-time evaluation
- ✅ Multi-LLM provider support (Google Gemini, OpenAI, Anthropic)
- ✅ Langfuse integration for LLM observability
- ✅ Cross-platform launcher scripts
- ✅ Theoretical framework and best practices guide

---

⭐ **Star this repository** if you find it helpful for your LLM evaluation projects!

📚 **Complete Documentation**: Check out `readme.adoc` for the complete theoretical framework and implementation guide for building LLM-as-a-Judge systems.

🚀 **Quick Start**: Use the provided launcher scripts (`start_web_app.bat` for Windows or `start_web_app.sh` for Linux/macOS) to get started immediately.
